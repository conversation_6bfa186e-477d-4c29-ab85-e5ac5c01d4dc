import withAuth from '../../../hooks/withAuth.tsx';
import Content from '../../app/layouts/content/Content.tsx';
import { ReactComponent as IconFolder } from '../../../assets/icons/icon-folder.svg';
import { ReactComponent as IconList } from '../../../assets/icons/icon-list.svg';
import { ReactComponent as IconPlus } from '../../../assets/icons/icon-plus.svg';
import { ReactComponent as IconCalendar } from '../../../assets/icons/icon-calendar.svg';
import { useClaimsContext } from '../ClaimsWrapper.tsx';
import ClaimStatusTag from '../../app/shared/ClaimStatusTag.tsx';
import { Claim, ClaimStatus, Estimate, EstimateStatus, WalkthroughType } from '../../../store/claims/types/Claim.ts';
import { Col, Row } from 'antd';
import Button, { ButtonSize, ButtonVariation } from '../../app/shared/Button.tsx';
import ClaimStepBox from './ClaimStepBox.tsx';
import Accordion from '../../app/shared/Accordion.tsx';
import { useEffect, useState } from 'react';
import UploadPdfModal from './estimate/UploadPdfModal.tsx';
import CreateEstimateModal from './estimate/CreateEstimateModal.tsx';
import { useParams } from 'react-router-dom';
import { useClaimStore } from '../../../store';
import ScheduleDateModal from './scheduleDate/ScheduleDateModal.tsx';
import AddCompanyCamModal from './AddCompanyCamModal.tsx';
import { openLinkInNewTab } from '../../../utils/openLinkInNewTab.ts';
import FlashMessages from '../../app/shared/FlashMessages.tsx';
import ViewEstimateModal from './estimate/ViewEstimateModal.tsx';
import Loader from '../../app/shared/Loader.tsx';
import { ItemType } from 'rc-collapse/es/interface';
import { getCompanyCamUrl } from '../../../utils/getCompanyCamUrl.ts';
import { getClaimStatusFromParam } from '../ClaimsWrapper.tsx';
import { convertToAMPM } from '../../../utils/convertToAMPM.ts';
import { format } from 'date-fns';
import { DATE_FORMAT_MONTHNAME } from '../../../common/dateFormats.ts';

const ClaimPage = () => {
    const [showScheduleModal, setShowScheduleModal] = useState<boolean>(false);
    const [showCompanyCamModal, setShowCompanyCamModal] = useState<boolean>(false);
    const [showPdfUpload, setShowPdfUpload] = useState<boolean>(false);
    const [showCreateEstimate, setShowCreateEstimate] = useState<boolean>(false);
    const [showViewEstimate, setShowViewEstimate] = useState<boolean>(false);
    const [estimateLoaded, setEstimateLoaded] = useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(true);
    const { type } = useClaimsContext();
    const [selectedFile, setSelectedFile] = useState<File>();
    const params = useParams();
    const { getClaim, getEstimate } = useClaimStore();
    const { setCurrentClaim, currentClaim } = useClaimsContext();
    const [estimate, setEstimate] = useState<Estimate>();
    
    const getFirstBoxOptions = (claim?: Claim) => {
        if (!claim) {
            return;
        }
        if (claim.walkthrough_type === WalkthroughType.SPS_HANDLES_WALKTHROUGH && !claim.walkthrough_scheduled_time && !claim.walkthrough_scheduled_date) {
            return;
        }
        if (claim.walkthrough_type === WalkthroughType.WALKTHROUGH_NOT_NEEDED && claim.company_cam) {
            return <Button
                color="primary"
                variant="outlined"
                buttonSize={ButtonSize.SMALL}
                onClick={() => {
                    if (claim.company_cam) {
                        openLinkInNewTab(getCompanyCamUrl(claim));
                    }
                }}
            >
                View Images
            </Button>
        }
        if (claim.walkthrough_scheduled_time && claim.walkthrough_scheduled_date) {
            const date = new Date(claim.walkthrough_scheduled_date);
            const fixedDate = new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate());
            const formattedDate = format(fixedDate, DATE_FORMAT_MONTHNAME);
            
            return <div className="flex-space-between">
                <div>
                    {formattedDate}
                    <br/>
                    {convertToAMPM(claim.walkthrough_scheduled_time)}
                </div>
                
                {(claim.images_uploaded && claim.company_cam) &&
                    <Button
                        color="primary"
                        variant="outlined"
                        buttonSize={ButtonSize.SMALL}
                        onClick={() => {
                            if (claim.company_cam) {
                                openLinkInNewTab(getCompanyCamUrl(claim));
                            }
                        }}
                    >
                        View Images
                    </Button>
                }
                
                {!claim.images_uploaded &&
                    <Button
                        color="primary"
                        variant="solid"
                        buttonSize={ButtonSize.SMALL}
                        onClick={() => setShowCompanyCamModal(true)}
                        icon={<IconPlus className="icon-regular"/>}
                    >
                        CompanyCam
                    </Button>
                }
            </div>
        }
        
        if (claim.walkthrough_type === WalkthroughType.AVAILABLE_DATES ||
            claim.walkthrough_type === WalkthroughType.VENDOR_SCHEDULES) {
            return <Button
                color="primary"
                variant="solid"
                buttonSize={ButtonSize.SMALL}
                onClick={() => setShowScheduleModal(true)}
                icon={<IconCalendar className="icon-regular"/>}
            >
                Schedule a date
            </Button>
        }
    }
    
    const getFirstBoxDescription = (claim?: Claim) => {
        if (!claim) {
            return;
        }
        
        if (claim.walkthrough_type === WalkthroughType.WALKTHROUGH_NOT_NEEDED) {
            if (claim.company_cam) {
                return 'No walkthrough needed. Please review the images to begin your estimating process.';
            }
            return 'The walkthrough is not needed. You can now start creating your estimate.';
        }
        
        if (claim.walkthrough_type === WalkthroughType.SPS_HANDLES_WALKTHROUGH) {
            if (claim.walkthrough_scheduled_time && claim.walkthrough_scheduled_date) {
                if (claim.images_uploaded) {
                    return 'Now that the walkthrough is complete, please review the images to start your estimation process.';
                }
                return 'After the walkthrough, upload the images to the CompanyCam link below.';
            }
            return 'SPS is coordinating with the homeowner. Await updates from your SPS rep.';
        }
        
        if (!claim.walkthrough_scheduled_time && !claim.walkthrough_scheduled_date) {
            
            if (claim.walkthrough_type === WalkthroughType.AVAILABLE_DATES) {
                return 'Click below to view available dates for completing an onsite walkthrough.';
            }
            if (claim.walkthrough_type === WalkthroughType.VENDOR_SCHEDULES) {
                return 'Click below to schedule a call with the homeowner.'
            }
        }
        
        if (claim.walkthrough_scheduled_time && claim.walkthrough_scheduled_date) {
            if (claim.images_uploaded) {
                return 'Now that the walkthrough is complete, please review the images to start your estimation process.';
            }
            return 'After the walkthrough, upload the images to the CompanyCam link below.';
        }
    }
    
    const getEstimateBoxOptions = (claim?: Claim, estimate?: Estimate ) => {
        if (!claim) {
            return;
        }
        
        if (estimate) {
            return <div className="flex-space-between">
                <Button
                    color="primary"
                    variant="link"
                    buttonSize={ButtonSize.SMALL}
                    variation={ButtonVariation.LINK_DARK}
                    onClick={() => {
                        if (estimate?.original_estimate) {
                            openLinkInNewTab(estimate?.original_estimate);
                        }
                    }}
                >
                    Original Estimate
                </Button>
                
                {
                    estimate?.status === EstimateStatus.DENIED ?
                        <Button
                            color="primary"
                            variant="outlined"
                            buttonSize={ButtonSize.SMALL}
                            onClick={() => setShowCreateEstimate(true)}
                        >
                            Edit Estimate
                        </Button>
                        :
                        <Button
                            color="primary"
                            variant="outlined"
                            buttonSize={ButtonSize.SMALL}
                            onClick={() => setShowViewEstimate(true)}
                        >
                            View Estimate
                        </Button>
                }
            </div>
        }
        
        return <Button
            buttonSize={ButtonSize.SMALL}
            color="primary"
            variant="solid"
            disabled={
                (!claim.walkthrough_scheduled_time && !claim.walkthrough_scheduled_date && claim.walkthrough_type !== WalkthroughType.WALKTHROUGH_NOT_NEEDED) ||
                (claim.walkthrough_type !== WalkthroughType.WALKTHROUGH_NOT_NEEDED && !currentClaim?.images_uploaded)
            }
            onClick={() => setShowPdfUpload(true)}
            icon={<IconList className="icon-regular"/>}
        >
            Create estimate
        </Button>
    }
    
    const getEstimateBoxTag = (estimate?: Estimate) => {
        if (!estimate?.status) {
            return;
        }
        if (estimate?.status === EstimateStatus.ACCEPTED) {
            return <div className="tag success">Approved</div>
        }
        
        if (estimate?.status === EstimateStatus.DENIED) {
            return <div className="tag pending">Re-estimate</div>
        }
        
        if ([EstimateStatus.DRAFT, EstimateStatus.PRESENTED, EstimateStatus.READY_TO_SEND].includes(estimate.status)) {
            return <div className="tag warn">In Review</div>
        }
    }
    
    const accordionItems = (claim?: Claim) :ItemType[] => {
        if (!claim) {
            return [];
        }
        const items: ItemType[] = [];
        
        items.push(
            {
                key: '1',
                label: 'Opportunity ID',
                children: <div>
                    <div className="accordion-content-meta">
                        <div className="accordion-content-meta-label">
                            Project Description
                        </div>
                        <div className="accordion-content-meta-text">
                            {claim.project_description}
                        </div>
                    </div>
                    <hr/>
                    <Row gutter={[24, 24]}>
                        <Col span={8}>
                            <div className="accordion-content-meta">
                                <div className="accordion-content-meta-label">
                                    Storage Required
                                </div>
                                <div className="accordion-content-meta-text">
                                    {claim.storage_required}
                                </div>
                            </div>
                        </Col>
                        <Col span={8}>
                            <div className="accordion-content-meta">
                                <div className="accordion-content-meta-label">
                                    Type of Move
                                </div>
                                <div className="accordion-content-meta-text">
                                    {claim.type_of_move.map((item, key) => <span key={key}>{item}{claim.type_of_move.length - 1 > key && ','} </span>)}
                                </div>
                            </div>
                        </Col>
                        <Col span={8}>
                            <div className="accordion-content-meta">
                                <div className="accordion-content-meta-label">
                                    COI Required
                                </div>
                                <div className="accordion-content-meta-text">
                                    {claim.coi_required === undefined ? '' : claim.coi_required ? 'Yes' : 'No'}
                                </div>
                            </div>
                        </Col>
                    </Row>
                </div>,
            }
        )
        items.push(
            {
                key: '2',
                label: 'SPS Project Coordinator',
                children: <div>
                    <Row gutter={[24, 24]}>
                        <Col span={8}>
                            <div className="accordion-content-meta">
                                <div className="accordion-content-meta-label">
                                    Name
                                </div>
                                <div className="accordion-content-meta-text">
                                    {claim.sps_coordinator_name}
                                </div>
                            </div>
                        </Col>
                        <Col span={8}>
                            <div className="accordion-content-meta">
                                <div className="accordion-content-meta-label">
                                    Email
                                </div>
                                <div className="accordion-content-meta-text">
                                    {claim.sps_coordinator_email}
                                </div>
                            </div>
                        </Col>
                        <Col span={8}>
                            <div className="accordion-content-meta">
                                <div className="accordion-content-meta-label">
                                    Phone Number
                                </div>
                                <div className="accordion-content-meta-text">
                                    {claim.sps_coordinator_phone}
                                </div>
                            </div>
                        </Col>
                    </Row>
                </div>,
            }
        )
        
        if (![ClaimStatus.CLOSED_LOST, ClaimStatus.CLOSED].includes(claim.status)) {
            items.push(
                {
                    key: '3',
                    label: 'Client Information',
                    children: <div>
                        <Row gutter={[24, 24]}>
                            <Col span={8}>
                                <div className="accordion-content-meta">
                                    <div className="accordion-content-meta-label">
                                        Name
                                    </div>
                                    <div className="accordion-content-meta-text">
                                        {claim.insured_name}
                                    </div>
                                </div>
                            </Col>
                            <Col span={8}>
                                <div className="accordion-content-meta">
                                    <div className="accordion-content-meta-label">
                                        Loss Street
                                    </div>
                                    <div className="accordion-content-meta-text">
                                        {claim.loss_street}
                                    </div>
                                </div>
                            </Col>
                            <Col span={8}>
                                <div className="accordion-content-meta">
                                    <div className="accordion-content-meta-label">
                                        Loss City
                                    </div>
                                    <div className="accordion-content-meta-text">
                                        {claim.loss_city}
                                    </div>
                                </div>
                            </Col>
                            <Col span={8}>
                                <div className="accordion-content-meta">
                                    <div className="accordion-content-meta-label">
                                        Loss State
                                    </div>
                                    <div className="accordion-content-meta-text">
                                        {claim.loss_state}
                                    </div>
                                </div>
                            </Col>
                            <Col span={8}>
                                <div className="accordion-content-meta">
                                    <div className="accordion-content-meta-label">
                                        Loss ZIP Code
                                    </div>
                                    <div className="accordion-content-meta-text">
                                        {claim.loss_zip}
                                    </div>
                                </div>
                            </Col>
                            <Col span={8}>
                                <div className="accordion-content-meta">
                                    <div className="accordion-content-meta-label">
                                        Classification of Loss
                                    </div>
                                    <div className="accordion-content-meta-text">
                                        {claim.classification_of_loss}
                                    </div>
                                </div>
                            </Col>
                            <Col span={8}>
                                <div className="accordion-content-meta">
                                    <div className="accordion-content-meta-label">
                                        sq ft of Home
                                    </div>
                                    <div className="accordion-content-meta-text">
                                        {claim.sq_ft_of_home}
                                    </div>
                                </div>
                            </Col>
                        </Row>
                    </div>,
                }
            )
        }
        
        return items;
    }
    
    useEffect(() => {
        if (!params?.id) {
            return;
        }
        getClaim(params.id).then((r) => {
            if (!r) {
                return;
            }
            setCurrentClaim(r);
        }).finally(() => setLoading(false));
        
    }, [getClaim, params.id]);
    
    useEffect(() => {
        if (!params?.id) {
            return;
        }
        getEstimate(params?.id).then((r) => {
            if (!r) {
                return;
            }
            setEstimate(r);
        }).finally(() => {
            setEstimateLoaded(true);
        });
    }, [getEstimate, params?.id]);
    
    useEffect(() => {
        return () => {
            setCurrentClaim(undefined);
            setEstimate(undefined);
            setSelectedFile(undefined);
        }
    }, []);
    
    if (loading) {
        return <Loader />
    }
    
    return (
        <div>
            <Content
                className="claim"
                breadcrumbItems={[
                    {
                        href: `/claims/${type}`,
                        title: (
                            <>
                                <IconFolder className="icon-regular"/>
                                <span>{getClaimStatusFromParam(type) ? getClaimStatusFromParam(type) : 'All Projects'}</span>
                            </>
                        ),
                    },
                    {
                        title: currentClaim?.claim_name,
                    }
                ]}
                headerTitle={
                    <div className="claim-header">
                        <h1 className="heading h2">{currentClaim?.claim_name}</h1>
                        <ClaimStatusTag status={currentClaim?.status}/>
                    </div>
                }
            >
                <Row gutter={[24, 24]} align="stretch" className="margin-bottom-32">
                    <Col span={8}>
                        <ClaimStepBox
                            actions={getFirstBoxOptions(currentClaim)}
                            description={getFirstBoxDescription(currentClaim)}
                            index={1}
                            showIcon={true}
                            title="Walkthrough"
                        />
                    </Col>
                    <Col span={8}>
                        <ClaimStepBox
                            actions={estimateLoaded && getEstimateBoxOptions(currentClaim, estimate)}
                            showIcon={estimate?.status === EstimateStatus.ACCEPTED}
                            index={2}
                            tag={getEstimateBoxTag(estimate)}
                            title="Estimate"
                        />
                    </Col>
                </Row>
                
                <Accordion
                    defaultActiveKey={[1, 2, 3]?.map((_, index) => index + 1)}
                    items={accordionItems(currentClaim)}
                />
            
            </Content>
            
            {
                showScheduleModal &&
                <ScheduleDateModal
                    show={showScheduleModal}
                    onClose={() => setShowScheduleModal(false)}
                />
            }
            {
                showCompanyCamModal &&
                <AddCompanyCamModal
                    show={showCompanyCamModal}
                    onClose={() => setShowCompanyCamModal(false)}
                />
            }
            
            {
                showPdfUpload &&
                <UploadPdfModal
                    show={showPdfUpload}
                    onClose={() => setShowPdfUpload(false)}
                    onSuccess={(file) => {
                        setSelectedFile(file)
                        setShowPdfUpload(false);
                        setShowCreateEstimate(true);
                    }}
                />
            }
            
            {
                (selectedFile || estimate) && showCreateEstimate &&
                <CreateEstimateModal
                    uploadedFile={selectedFile || estimate?.original_estimate || ''}
                    onChangeFile={() => setShowPdfUpload(true)}
                    onClose={() => setShowCreateEstimate(false)}
                    onSuccess={(estimate) => {
                        setShowCreateEstimate(false);
                        FlashMessages.success('Estimate created!');
                        setEstimate(estimate);
                    }}
                    show={showCreateEstimate}
                    estimate={estimate?.status === EstimateStatus.DENIED ? estimate : undefined}
                />
            }
            
            {
                showViewEstimate && estimate?.items &&
                <ViewEstimateModal
                    onClose={() => setShowViewEstimate(false)}
                    show={showViewEstimate}
                    items={estimate?.items}
                />
            }
        </div>
    );
};

export default withAuth(ClaimPage);
