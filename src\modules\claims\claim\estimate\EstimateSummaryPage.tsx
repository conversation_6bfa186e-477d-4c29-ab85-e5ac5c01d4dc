import React from 'react';
import Content from '../../../app/layouts/content/Content.tsx';
import { EstimateItem } from '../../../../store/claims/types/Claim.ts';
import { Col, Row } from 'antd';
import { formatCurrency } from '../../../../utils/helpers.ts';
import Button from '../../../app/shared/Button.tsx';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useClaimsContext } from '../../ClaimsWrapper.tsx';

interface Props {
    items: EstimateItem[];
    onSubmitForApproval: VoidFunction;
}

const EstimateSummaryPage: React.FC<Props> = ({
    items,
    onSubmitForApproval,
}) => {
    const navigate = useNavigate();
    const params = useParams();
    const location = useLocation();
    const { currentClaim } = useClaimsContext();

    const onGoBack = () => {
        navigate(`/claims/${params.type}/${params.id}/estimate/create`);
    };

    const onClose = () => {
        navigate(`/claims/${params.type}/${params.id}`);
    };

    const groupedItems = items.reduce<Record<string, EstimateItem[]>>((acc, product) => {
        acc[product.product_family] = acc[product.product_family] || [];
        acc[product.product_family].push(product);
        return acc;
    }, {});

    const calculateSubtotal = (items: EstimateItem[]) => {
        return items.reduce((sum, item) => {
            const baseTotal = item.unit_price * item.quantity;
            // For Storage items, multiply by length if it exists
            if (item.product_family === 'Storage' && (item as any).length) {
                return sum + (baseTotal * (item as any).length);
            }
            return sum + baseTotal;
        }, 0);
    };

    const calculateTotal = () => {
        return Object.values(groupedItems).reduce((sum, items) => sum + calculateSubtotal(items), 0);
    };

    const renderServiceSection = (category: string, items: EstimateItem[]) => {
        const isStorage = category === 'Storage';

        return (
            <div key={category} className="estimate-summary-section">
                <div className="estimate-summary-section-header">
                    {category}
                </div>
                <div className="estimate-summary-section-content">
                    <div className="estimate-summary-section-table">
                        <Row className="estimate-summary-header-row">
                            <Col span={6}>Service Title</Col>
                            <Col span={4}>{isStorage ? 'Qty' : 'Hours'}</Col>
                            <Col span={4}>Rate</Col>
                            {isStorage && <Col span={4}>Length</Col>}
                            <Col span={isStorage ? 6 : 10} className="end">Total</Col>
                        </Row>
                        {items.map((item, index) => (
                            <Row key={index} className="estimate-summary-item-row">
                                <Col span={6}>{item.product_name}</Col>
                                <Col span={4}>
                                    {isStorage ? item.quantity : `${item.quantity} hours`}
                                </Col>
                                <Col span={4}>{formatCurrency(item.unit_price)}</Col>
                                {isStorage && (
                                    <Col span={4}>
                                        {(item as any).length || 1}
                                    </Col>
                                )}
                                <Col span={isStorage ? 6 : 10} className="end">
                                    {formatCurrency((() => {
                                        const baseTotal = item.unit_price * item.quantity;
                                        // For Storage items, multiply by length if it exists
                                        if (item.product_family === 'Storage' && (item as any).length) {
                                            return baseTotal * (item as any).length;
                                        }
                                        return baseTotal;
                                    })())}
                                </Col>
                            </Row>
                        ))}
                    </div>
                </div>
                <div className="estimate-summary-subtotal">
                    <span className="estimate-summary-subtotal-label">{category} Subtotal</span>
                    <span className="estimate-summary-subtotal-value">
                        {formatCurrency(calculateSubtotal(items))}
                    </span>
                </div>
            </div>
        );
    };

    return (
        <Content
            headerTitle="Estimate Summary"
            headerSubtitle="Please verify your estimation before sending for Approval."
            breadcrumbItems={[
                { title: 'Projects', href: `/claims/${params.type}` },
                { title: currentClaim?.claim_name || 'Claim', href: `/claims/${params.type}/${params.id}` },
                { title: 'Add Services', href: `/claims/${params.type}/${params.id}/estimate/create` },
                { title: 'Estimate Summary' },
            ]}
        >
            <div className="estimate-summary-content">
                {Object.entries(groupedItems).map(([category, items]) =>
                    renderServiceSection(category, items)
                )}
                
                <div className="estimate-summary-total">
                    <span className="estimate-summary-total-label">Total Estimate</span>
                    <span className="estimate-summary-total-value">
                        {formatCurrency(calculateTotal())}
                    </span>
                </div>
            </div>

            <div className="estimate-summary-actions">
                <Button
                    color="default"
                    variant="outlined"
                    onClick={onGoBack}
                    className="estimate-summary-back-button"
                >
                    GO BACK
                </Button>
                <Button
                    color="primary"
                    variant="solid"
                    onClick={onSubmitForApproval}
                    className="estimate-summary-submit-button"
                >
                    SUBMIT FOR APPROVAL
                </Button>
            </div>
        </Content>
    );
};

export default EstimateSummaryPage;
