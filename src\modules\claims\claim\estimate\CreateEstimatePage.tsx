import React, { useEffect, useState } from 'react';
import Content from '../../../app/layouts/content/Content.tsx';
import CustomForm from '../../../app/shared/form/Form.tsx';
import { Col, Form, Row, Select, Input } from 'antd';
import Button, { ButtonVariation } from '../../../app/shared/Button.tsx';
import ClaimStatusTag from '../../../app/shared/ClaimStatusTag.tsx';
import {
    Estimate,
    EstimateCreateProduct,
    EstimateItem,
    PriceBookItem,
} from '../../../../store/claims/types/Claim.ts';
import { ReactComponent as IconPlus } from '../../../../assets/icons/icon-plus.svg';
import { ReactComponent as IconMinusFilled } from '../../../../assets/icons/icon-minus-rounded-fill.svg';
import { useClaimStore, useFileStore } from '../../../../store';
import { useClaimsContext } from '../../ClaimsWrapper.tsx';
import { formatCurrency, formatNumberOutput } from '../../../../utils/helpers.ts';
import Loader from '../../../app/shared/Loader.tsx';
import classNames from 'classnames';
import { calculateTotalUnitPrice, PriceBookSortingGroups } from './utils.ts';
import Dialog from '../../../app/shared/Dialog.tsx';
import FlashMessages from '../../../app/shared/FlashMessages.tsx';

import EstimateConfirmationModal from './EstimateConfirmationModal.tsx';
import FileViewer from '../../../app/shared/FileViewer.tsx';
import FileUpload from '../../../app/shared/form/FileUpload.tsx';
import { useNavigate, useParams } from 'react-router-dom';

interface Props {
    onChangeFile?: (newFile: File) => void;
    uploadedFile: File | string;
    estimate?: Estimate;
}

const CreateEstimatePage: React.FC<Props> = ({
    estimate,
    uploadedFile,
}) => {
    const navigate = useNavigate();
    const params = useParams();
    const [form] = Form.useForm();
    const formValues = Form.useWatch([], form);
    const { getClaimPriceBook, setEstimate, getClaimEstimateDocumentLink } = useClaimStore();
    const { uploadFileS3 } = useFileStore();
    const { currentClaim } = useClaimsContext();
    const [groupedProducts, setGroupedProducts] = useState<Record<string, PriceBookItem[]>>({});
    const [canSubmit, setCanSubmit] = useState<boolean>(true);
    const [infoExpanded, setInfoExpanded] = useState<boolean>(false);
    const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);
    const [priceBookLoading, setPriceBookLoading] = useState<boolean>(false);
    const [estimateItems, setEstimateItems] = useState<Record<string, EstimateItem[]>>({});
    const [groupSelectEnabled, setGroupSelectEnabled] = useState<Record<string, boolean>>({});
    const [selectedTab, setSelectedTab] = useState<string>('');
    const [showConfirmationModal, setShowConfirmationModal] = useState<boolean>(false);
    const [tempFormValues, setTempFormValues] = useState<Record<string, any>>({});
    const [showPreviewModal, setShowPreviewModal] = useState<boolean>(false);
    const [showFileUploadModal, setShowFileUploadModal] = useState<boolean>(false);
    const [newUploadedFile, setNewUploadedFile] = useState<File | null>(null);
    const [currentFile, setCurrentFile] = useState<File | string>(uploadedFile);

    const groupNames = Object.keys(groupedProducts);

    const onClose = () => {
        navigate(`/claims/${params.type}/${params.id}`);
    };

    const onSuccess = (estimate: Estimate) => {
        navigate(`/claims/${params.type}/${params.id}`);
    };

    // Load price book data
    useEffect(() => {
        if (!currentClaim?.id) {
            return;
        }

        setPriceBookLoading(true);
        getClaimPriceBook(currentClaim.id)
            .then((data) => {
                console.log('Price book data received:', data);
                
                // Group products by product_family and sort according to PriceBookSortingGroups
                const grouped = data.reduce<Record<string, PriceBookItem[]>>((acc, product) => {
                    acc[product.product_family] = acc[product.product_family] || [];
                    acc[product.product_family].push(product);
                    return acc;
                }, {});

                // Sort the groups according to PriceBookSortingGroups order
                const sortedGrouped: Record<string, PriceBookItem[]> = {};
                PriceBookSortingGroups.forEach(groupName => {
                    if (grouped[groupName]) {
                        sortedGrouped[groupName] = grouped[groupName];
                    }
                });

                // Add any remaining groups that weren't in the sorting list
                Object.keys(grouped).forEach(groupName => {
                    if (!PriceBookSortingGroups.includes(groupName)) {
                        sortedGrouped[groupName] = grouped[groupName];
                    }
                });

                console.log('Grouped and sorted products:', sortedGrouped);
                setGroupedProducts(sortedGrouped);
            })
            .catch((error) => {
                console.error('Error loading price book:', error);
                FlashMessages.error('Failed to load price book data');
            })
            .finally(() => {
                setPriceBookLoading(false);
            });
    }, [currentClaim?.id, getClaimPriceBook]);

    // Load existing estimate data if editing
    useEffect(() => {
        if (!estimate?.id || !currentClaim?.id) {
            return;
        }

        console.log('Loading existing estimate:', estimate);
        
        // Group estimate items by product_family
        const groupedEstimateItems = estimate.estimate_items.reduce<Record<string, EstimateItem[]>>((acc, item) => {
            acc[item.product_family] = acc[item.product_family] || [];
            acc[item.product_family].push(item);
            return acc;
        }, {});

        console.log('Grouped estimate items:', groupedEstimateItems);
        setEstimateItems(groupedEstimateItems);

        // Set form values for each group
        Object.keys(groupedEstimateItems).forEach((groupName) => {
            const items = groupedEstimateItems[groupName];
            const formData = items.map((item) => ({
                pricebook_entry_id: item.pricebook_entry_id,
                product_id: item.product_id,
                unit_price: formatNumberOutput(item.unit_price),
                quantity: item.quantity,
                length: (item as any).length || undefined,
            }));
            
            console.log(`Setting form data for ${groupName}:`, formData);
            form.setFieldValue(groupName, formData);
        });
    }, [estimate, currentClaim?.id, form]);

    // Set initial selected tab
    useEffect(() => {
        if (!selectedTab && Object.keys(groupedProducts).length > 0) {
            const firstTab = Object.keys(groupedProducts)[0];
            console.log('Setting initial selectedTab to:', firstTab);
            setSelectedTab(firstTab);
        }
    }, [groupedProducts]);

    // Debug form values
    useEffect(() => {
        console.log('Form values changed:', formValues);
        console.log('Group select enabled:', groupSelectEnabled);
        console.log('Temp form values:', tempFormValues);
        console.log('Selected tab:', selectedTab);
    }, [formValues, groupSelectEnabled, tempFormValues, selectedTab]);

    const handleGroupSelectEnabled = (groupSelect: string, value: boolean) => {
        setGroupSelectEnabled((prevState) => ({
            ...prevState,
            [groupSelect]: value,
        }));
    };

    const onSubmit = async (values: any) => {
        console.log('=== FORM SUBMISSION DEBUG ===');
        console.log('Form submission values:', JSON.stringify(values, null, 2));
        
        if (!currentClaim?.id) {
            console.error('No current claim ID available');
            FlashMessages.error('No claim selected');
            return;
        }

        setCanSubmit(false);

        try {
            // Get all form data from all tabs
            const allFormData = form.getFieldsValue();
            console.log('All form data from getFieldsValue:', JSON.stringify(allFormData, null, 2));

            // Process form data into estimate products
            const estimateProducts: EstimateCreateProduct[] = [];

            Object.keys(allFormData).forEach((groupName) => {
                const groupData = allFormData[groupName];
                console.log(`Processing group ${groupName}:`, groupData);
                
                if (Array.isArray(groupData) && groupData.length > 0) {
                    groupData.forEach((item: any) => {
                        if (item && item.pricebook_entry_id && item.unit_price && item.quantity) {
                            const product: EstimateCreateProduct = {
                                pricebook_entry_id: item.pricebook_entry_id,
                                unit_price: parseFloat(item.unit_price.toString().replace(/,/g, '')),
                                quantity: parseInt(item.quantity.toString()),
                            };

                            // Add length for Storage items
                            if (groupName === 'Storage' && item.length) {
                                (product as any).length = parseInt(item.length.toString());
                            }

                            console.log(`Adding product for ${groupName}:`, product);
                            estimateProducts.push(product);
                        }
                    });
                }
            });

            console.log('Final estimate products:', estimateProducts);

            if (estimateProducts.length === 0) {
                FlashMessages.error('Please add at least one service item');
                setCanSubmit(true);
                return;
            }

            // Upload file if there's a new one
            let fileUrl = '';
            if (newUploadedFile) {
                console.log('Uploading new file:', newUploadedFile.name);
                const documentLinkResponse = await getClaimEstimateDocumentLink(currentClaim.id);
                await uploadFileS3(newUploadedFile, documentLinkResponse.upload_url);
                fileUrl = documentLinkResponse.file_url;
                console.log('File uploaded successfully, URL:', fileUrl);
            } else if (typeof currentFile === 'string') {
                fileUrl = currentFile;
                console.log('Using existing file URL:', fileUrl);
            }

            // Create or update estimate
            const estimateData = {
                claim_id: currentClaim.id,
                estimate_products: estimateProducts,
                ...(fileUrl && { estimate_document_url: fileUrl }),
            };

            console.log('Submitting estimate data:', estimateData);

            const result = await setEstimate(estimateData, estimate?.id);
            console.log('Estimate created/updated successfully:', result);
            
            FlashMessages.success(estimate?.id ? 'Estimate updated successfully!' : 'Estimate created successfully!');
            onSuccess(result);

        } catch (error) {
            console.error('Error submitting estimate:', error);
            FlashMessages.error('Failed to save estimate. Please try again.');
        } finally {
            setCanSubmit(true);
        }
    };

    const handleEstimateSummary = () => {
        console.log('=== ESTIMATE SUMMARY DEBUG ===');
        console.log('Getting estimate summary for all tabs');
        console.log('Available group names:', groupNames);
        console.log('Current selectedTab:', selectedTab);

        // Get ALL form data from ALL tabs
        const allFormValues = form.getFieldsValue();
        console.log('All form values:', JSON.stringify(allFormValues, null, 2));

        const items: EstimateItem[] = [];

        groupNames.forEach((category) => {
            console.log(`\n--- Processing ${category} ---`);
            const categoryData = allFormValues[category];
            console.log(`${category} data:`, categoryData);

            if (Array.isArray(categoryData) && categoryData.length > 0) {
                categoryData.forEach((item: any, index: number) => {
                    console.log(`Processing ${category} item ${index}:`, item);
                    
                    if (item && item.pricebook_entry_id && item.unit_price && item.quantity) {
                        // Find the product details from groupedProducts
                        const productDetails = groupedProducts[category]?.find(
                            (product) => product.id === item.pricebook_entry_id
                        );

                        if (productDetails) {
                            const estimateItem: EstimateItem = {
                                id: `temp-${Date.now()}-${index}`,
                                pricebook_entry_id: item.pricebook_entry_id,
                                product_family: category,
                                product_id: productDetails.product_id,
                                product_name: productDetails.product_name,
                                unit_price: parseFloat(item.unit_price.toString().replace(/,/g, '')),
                                quantity: parseInt(item.quantity.toString()),
                                estimate_id: estimate?.id || '',
                            };

                            // Add length for Storage items
                            if (category === 'Storage' && item.length) {
                                (estimateItem as any).length = parseInt(item.length.toString());
                                console.log(`Added length ${item.length} to Storage item`);
                            }

                            console.log(`Created estimate item:`, estimateItem);
                            items.push(estimateItem);
                        } else {
                            console.warn(`Product details not found for pricebook_entry_id: ${item.pricebook_entry_id} in category: ${category}`);
                        }
                    } else {
                        console.log(`Skipping incomplete item in ${category}:`, item);
                    }
                });
            } else {
                console.log(`No data found for ${category} or data is not an array`);
            }
            console.log(`--- End processing ${category} ---\n`);
        });

        console.log('Final estimate items for summary:', items);
        console.log('Total items found:', items.length);
        console.log('=== END ESTIMATE SUMMARY DEBUG ===');

        // Navigate to summary page with the items and form data
        navigate(`/claims/${params.type}/${params.id}/estimate/summary`, {
            state: {
                items,
                formData: allFormValues,
                newUploadedFile,
                currentFile,
                estimate
            }
        });
    };

    const handleConfirmationYes = () => {
        setShowConfirmationModal(false);
        handleEstimateSummary();
    };

    const handleConfirmationNo = () => {
        setShowConfirmationModal(false);
    };

    const handlePreviewFile = () => {
        setShowPreviewModal(true);
    };

    const handleChangeFile = () => {
        // Open the internal file upload modal for file replacement
        setShowFileUploadModal(true);
    };

    const handleFileUpload = (file: File) => {
        setNewUploadedFile(file);
        setCurrentFile(file);
        setShowFileUploadModal(false);
        FlashMessages.success('File uploaded successfully!');
    };

    const generateAccordionItems = (groupedProducts: Record<string, PriceBookItem[]>) => {
        return Object.keys(groupedProducts).map((groupName, index) => {
            const products = groupedProducts[groupName];
            
            return {
                key: index + 1,
                label: groupName,
                children: (
                    <div>
                        {products.map((product) => (
                            <div key={product.id} className="product-item">
                                <h6>{product.product_name}</h6>
                                <p>{product.description || 'No description available'}</p>
                            </div>
                        ))}
                    </div>
                ),
            };
        });
    };

    const accordionItems = generateAccordionItems(groupedProducts);

    return (
        <Content
            headerTitle="Add Services"
            breadcrumbItems={[
                { title: 'Projects', href: `/claims/${params.type}` },
                { title: currentClaim?.claim_name || 'Claim', href: `/claims/${params.type}/${params.id}` },
                { title: 'Add Services' },
            ]}
        >
            <div className="claim-ce" style={{ display: 'flex', gap: '24px' }}>
                {/* Side content equivalent */}
                <div className="claim-ce-sidebar" style={{ flex: '0 0 300px', background: '#f8f9fa', padding: '20px', borderRadius: '8px' }}>
                    <div className="claim-header">
                        <h1 className="heading h3">{currentClaim?.claim_name}</h1>
                        <ClaimStatusTag status={currentClaim?.status} />
                    </div>

                    <div className="claim-ce-content">
                        <div className="claim-ce-info" style={{ marginBottom: '0px' }}>
                            <div
                                className={classNames('claim-ce-info-inner', {
                                    expanded: infoExpanded,
                                })}
                                style={{ background: 'none', paddingTop: '0px' }}
                            >
                                <h5 className="claim-ce-info-title">Create your estimate</h5>
                                <div className="claim-ce-info-group">
                                    <p className="claim-ce-info-group-description">
                                        Now it's time to input your line items. Using our
                                        service page, select a service and enter the
                                        corresponding rate and quantity.
                                        <div
                                            className="claim-ce-info-action"
                                            onClick={() => setInfoExpanded(!infoExpanded)}
                                        >
                                            {infoExpanded ? 'Less' : 'Learn More'}
                                        </div>
                                    </p>
                                </div>

                                {infoExpanded && (
                                    <>
                                        <div className="claim-ce-info-group">
                                            <h6 className="claim-ce-info-group-title">
                                                Helpful Tips!
                                            </h6>
                                            <p className="claim-ce-info-group-description">
                                                For materials, group them under one service and
                                                input the total cost for all items from your
                                                original estimate. This will streamline the
                                                process and ensure accuracy. Pack Out = Move
                                                Out. Pack Back = Move Back.
                                            </p>
                                        </div>
                                        <div className="claim-ce-info-group">
                                            <p className="claim-ce-info-group-description">
                                                <b>Storage:</b> Select the type of storage
                                                required (e.g., vault or lb) and enter the
                                                estimated storage needed in Qty. Storage is
                                                estimated for one month.
                                            </p>
                                        </div>
                                    </>
                                )}
                            </div>
                        </div>

                        <div className="claim-ce-viewer-head" style={{ padding: '0 30px' }}>
                            <h6 className="claim-ce-viewer-head-title">Original estimate</h6>

                            <div className="claim-ce-viewer-head-actions">
                                <Button
                                    color="primary"
                                    variant="link"
                                    variation={ButtonVariation.LINK}
                                    onClick={handlePreviewFile}
                                    className="claim-ce-viewer-head-actions-preview"
                                >
                                    Preview
                                </Button>
                                <Button
                                    color="primary"
                                    variant="link"
                                    variation={ButtonVariation.LINK}
                                    onClick={handleChangeFile}
                                    className="claim-ce-viewer-head-actions-change"
                                >
                                    Change
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Main content */}
                <div className="claim-ce-main" style={{ flex: '1' }}>
                    {!canSubmit && <Loader className="claim-ce-loader" />}
                    <CustomForm form={form} onSubmit={onSubmit} className="claim-ce-form">
                        {priceBookLoading && <Loader />}

                        {!priceBookLoading && groupNames.length > 0 && (
                            <div className="claim-ce-tab-layout">
                                <div className="claim-ce-tabs">
                                    {groupNames.map((group: string) => (
                                        <div
                                            key={group}
                                            className={`claim-ce-tab${selectedTab === group ? ' selected' : ''}`}
                                            onClick={() => {
                                                console.log('Switching to tab:', group);
                                                setSelectedTab(group);
                                            }}
                                        >
                                            {group}
                                        </div>
                                    ))}
                                </div>
                                <div className="claim-ce-tab-content">
                                    {/* Render the table and form for the selected tab only */}
                                    {(() => {
                                        const groupName = selectedTab;
                                        const selectOptions =
                                            groupedProducts[groupName]?.map((product) => {
                                                const estimateProductItem = estimateItems[groupName]?.find(
                                                    (item) => item.product_id === product.product_id,
                                                );
                                                return {
                                                    pricebook_entry_id: product.id,
                                                    label: product.product_name,
                                                    name: product.product_name,
                                                    value: product.id,
                                                    unit_price: estimateProductItem?.unit_price
                                                        ? formatNumberOutput(estimateProductItem.unit_price)
                                                        : undefined,
                                                    product_id: product.product_id,
                                                    quantity: estimateProductItem?.quantity
                                                        ? estimateProductItem.quantity
                                                        : 1,
                                                };
                                            }) || [];
                                        const initialValue =
                                            (!!Object.keys(estimateItems).length &&
                                                estimateItems[groupName] &&
                                                selectOptions.filter((item) =>
                                                    estimateItems[groupName].find(
                                                        (estimateItem) =>
                                                            estimateItem.product_id === item.product_id,
                                                    ),
                                                )) ||
                                            [];
                                        return (
                                            <Form.List name={groupName} initialValue={initialValue}>
                                                {(fields, { add, remove }) => (
                                                    <>
                                                        <div className="claim-ce-form-boxes-item-head">
                                                            <Row
                                                                gutter={16}
                                                                align="bottom"
                                                                style={{ width: '100%' }}
                                                            >
                                                                <Col span={6}>
                                                                    <label className="claim-ce-form-boxes-item-head-label">
                                                                        Service Title
                                                                    </label>
                                                                </Col>
                                                                <Col span={3}>
                                                                    <label className="claim-ce-form-boxes-item-head-label">
                                                                        #
                                                                    </label>
                                                                </Col>
                                                                <Col span={3}>
                                                                    <label className="claim-ce-form-boxes-item-head-label">
                                                                        Rate
                                                                    </label>
                                                                </Col>
                                                                {groupName === 'Storage' && (
                                                                    <Col span={3}>
                                                                        <label className="claim-ce-form-boxes-item-head-label">
                                                                            Length
                                                                        </label>
                                                                    </Col>
                                                                )}
                                                                <Col span={groupName === 'Storage' ? 3 : 6}>
                                                                    <label className="claim-ce-form-boxes-item-head-label">
                                                                        Total
                                                                    </label>
                                                                </Col>
                                                                <Col span={3}>
                                                                    <label className="claim-ce-form-boxes-item-head-label">
                                                                        Action
                                                                    </label>
                                                                </Col>
                                                            </Row>
                                                        </div>
                                                        <div className="claim-ce-form-boxes-item-body">
                                                            {fields.map(({ key, name, ...restField }) => {
                                                                const currentFormValues = form.getFieldsValue();
                                                                const currentItem = currentFormValues[groupName]?.[name];
                                                                const unitPrice = currentItem?.unit_price || 0;
                                                                const quantity = currentItem?.quantity || 0;
                                                                const length = currentItem?.length || 1;

                                                                let total = parseFloat(unitPrice.toString().replace(/,/g, '')) * parseInt(quantity.toString());
                                                                if (groupName === 'Storage' && length) {
                                                                    total = total * parseInt(length.toString());
                                                                }

                                                                return (
                                                                    <Row
                                                                        key={key}
                                                                        gutter={16}
                                                                        align="middle"
                                                                        style={{ width: '100%' }}
                                                                        className="claim-ce-form-boxes-item-body-row"
                                                                    >
                                                                        <Col span={6}>
                                                                            <Form.Item
                                                                                {...restField}
                                                                                name={[name, 'pricebook_entry_id']}
                                                                                rules={[
                                                                                    {
                                                                                        required: true,
                                                                                        message: 'Please select a service',
                                                                                    },
                                                                                ]}
                                                                            >
                                                                                <Select
                                                                                    placeholder="Select a service"
                                                                                    options={selectOptions}
                                                                                    onChange={(value) => {
                                                                                        const selectedProduct = selectOptions.find(
                                                                                            (option) => option.value === value,
                                                                                        );
                                                                                        if (selectedProduct) {
                                                                                            form.setFieldValue(
                                                                                                [groupName, name, 'unit_price'],
                                                                                                selectedProduct.unit_price || '',
                                                                                            );
                                                                                            form.setFieldValue(
                                                                                                [groupName, name, 'quantity'],
                                                                                                selectedProduct.quantity || 1,
                                                                                            );
                                                                                            form.setFieldValue(
                                                                                                [groupName, name, 'product_id'],
                                                                                                selectedProduct.product_id,
                                                                                            );
                                                                                        }
                                                                                    }}
                                                                                />
                                                                            </Form.Item>
                                                                        </Col>
                                                                        <Col span={3}>
                                                                            <Form.Item
                                                                                {...restField}
                                                                                name={[name, 'quantity']}
                                                                                rules={[
                                                                                    {
                                                                                        required: true,
                                                                                        message: 'Required',
                                                                                    },
                                                                                ]}
                                                                            >
                                                                                <Input
                                                                                    type="number"
                                                                                    min={1}
                                                                                    placeholder="1"
                                                                                />
                                                                            </Form.Item>
                                                                        </Col>
                                                                        <Col span={3}>
                                                                            <Form.Item
                                                                                {...restField}
                                                                                name={[name, 'unit_price']}
                                                                                rules={[
                                                                                    {
                                                                                        required: true,
                                                                                        message: 'Required',
                                                                                    },
                                                                                ]}
                                                                            >
                                                                                <Input
                                                                                    placeholder="0.00"
                                                                                    prefix="$"
                                                                                />
                                                                            </Form.Item>
                                                                        </Col>
                                                                        {groupName === 'Storage' && (
                                                                            <Col span={3}>
                                                                                <Form.Item
                                                                                    {...restField}
                                                                                    name={[name, 'length']}
                                                                                    rules={[
                                                                                        {
                                                                                            required: true,
                                                                                            message: 'Required',
                                                                                        },
                                                                                    ]}
                                                                                >
                                                                                    <Input
                                                                                        type="number"
                                                                                        min={1}
                                                                                        placeholder="1"
                                                                                    />
                                                                                </Form.Item>
                                                                            </Col>
                                                                        )}
                                                                        <Col span={groupName === 'Storage' ? 3 : 6}>
                                                                            <div className="claim-ce-form-boxes-item-total">
                                                                                {formatCurrency(total || 0)}
                                                                            </div>
                                                                        </Col>
                                                                        <Col span={3}>
                                                                            <Button
                                                                                color="danger"
                                                                                variant="link"
                                                                                onClick={() => remove(name)}
                                                                                icon={<IconMinusFilled />}
                                                                                className="claim-ce-form-boxes-item-remove"
                                                                            />
                                                                        </Col>
                                                                        <Form.Item
                                                                            {...restField}
                                                                            name={[name, 'product_id']}
                                                                            hidden
                                                                        >
                                                                            <Input type="hidden" />
                                                                        </Form.Item>
                                                                    </Row>
                                                                );
                                                            })}
                                                        </div>
                                                        <div className="claim-ce-form-boxes-item-actions">
                                                            <Button
                                                                color="primary"
                                                                variant="link"
                                                                onClick={() => add()}
                                                                icon={<IconPlus />}
                                                                className="claim-ce-form-boxes-item-add"
                                                            >
                                                                Add Service
                                                            </Button>
                                                        </div>
                                                        {fields.length > 0 && (
                                                            <>
                                                                <div className="claim-ce-form-boxes-item-footer">
                                                                    <div className="claim-ce-form-boxes-item-footer-content">
                                                                        <div className="claim-ce-form-boxes-item-footer-total">
                                                                            <span className="claim-ce-form-boxes-item-footer-total-label">
                                                                                Total
                                                                            </span>
                                                                            <span className="claim-ce-form-boxes-item-footer-total-value">
                                                                                {formatCurrency(
                                                                                    (() => {
                                                                                        // Get all form values to ensure we have data from all tabs
                                                                                        const allFormValues = form.getFieldsValue();
                                                                                        const groupData = allFormValues[groupName];

                                                                                        return (groupData?.reduce?.(
                                                                                            (sum: number, item: any) => {
                                                                                                const baseTotal = Number(item.unit_price) * Number(item.quantity);
                                                                                                // For Storage items, multiply by length if it exists
                                                                                                if (groupName === 'Storage' && item.length) {
                                                                                                    return sum + (baseTotal * Number(item.length));
                                                                                                }
                                                                                                return sum + baseTotal;
                                                                                            },
                                                                                            0,
                                                                                        )) || 0;
                                                                                    })()
                                                                                )}
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                {/* Subtotal row */}
                                                                <div className="claim-ce-form-boxes-item-subtotal-row">
                                                                    <span className="claim-ce-form-boxes-item-subtotal-label">
                                                                        {groupName} Subtotal
                                                                    </span>
                                                                    <span className="claim-ce-form-boxes-item-subtotal-value">
                                                                        {formatCurrency(
                                                                            (() => {
                                                                                // Get all form values to ensure we have data from all tabs
                                                                                const allFormValues = form.getFieldsValue();
                                                                                const groupData = allFormValues[groupName];

                                                                                return (groupData?.reduce?.(
                                                                                    (sum: number, item: any) => {
                                                                                        const baseTotal = Number(item.unit_price) * Number(item.quantity);
                                                                                        // For Storage items, multiply by length if it exists
                                                                                        if (groupName === 'Storage' && item.length) {
                                                                                            return sum + (baseTotal * Number(item.length));
                                                                                        }
                                                                                        return sum + baseTotal;
                                                                                    },
                                                                                    0,
                                                                                )) || 0;
                                                                            })()
                                                                        )}
                                                                    </span>
                                                                </div>
                                                            </>
                                                        )}
                                                    </Form.List>
                                                );
                                            })()}
                                        </div>
                                    </div>
                                )}

                                {!priceBookLoading && !accordionItems.length && (
                                    <p>
                                        We're updating the list of available services and it'll be ready soon! If
                                        you need any help in the meantime, don't hesitate to reach out to us.
                                    </p>
                                )}
                            </CustomForm>

                            {/* ESTIMATE SUMMARY Button */}
                            <div className="estimate-summary-button-container">
                                <Button
                                    color="primary"
                                    variant="solid"
                                    onClick={() => setShowConfirmationModal(true)}
                                    className="estimate-summary-button"
                                    size="large"
                                >
                                    ESTIMATE SUMMARY
                                </Button>
                            </div>

                </div>
            </div>

            {showConfirmModal && (
                <Dialog
                    show={showConfirmModal}
                    onClose={() => setShowConfirmModal(false)}
                    onSuccess={() => onClose()}
                    description="If you leave this page, all the information you've entered will be lost. Are you sure you want to continue?"
                />
            )}

            {showConfirmationModal && (
                <EstimateConfirmationModal
                    show={showConfirmationModal}
                    onClose={() => setShowConfirmationModal(false)}
                    onYes={handleConfirmationYes}
                    onNo={handleConfirmationNo}
                />
            )}

            {showPreviewModal && (
                <FileViewer
                    show={showPreviewModal}
                    onClose={() => setShowPreviewModal(false)}
                    file={currentFile}
                />
            )}

            {showFileUploadModal && (
                <FileUpload
                    show={showFileUploadModal}
                    onClose={() => setShowFileUploadModal(false)}
                    onUpload={handleFileUpload}
                    title="Upload New Estimate Document"
                    description="Select a new file to replace the current estimate document."
                />
            )}
        </Content>
    );
};

export default CreateEstimatePage;
