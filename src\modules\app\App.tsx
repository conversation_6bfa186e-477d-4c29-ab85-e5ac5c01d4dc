import { BrowserRouter, Route, Routes, Navigate } from 'react-router-dom';
import LoginPage from '../auth/LoginPage.tsx';
import ForgotPasswordPage from '../auth/ForgotPasswordPage.tsx';
import ResetPasswordPage from '../auth/ResetPasswordPage.tsx';
import RegistrationPage from '../auth/registration/RegistrationPage.tsx';
import ClaimsPage from '../claims/ClaimsList.tsx';
import ClaimPage from '../claims/claim/ClaimPage.tsx';
import ClaimsWrapper from '../claims/ClaimsWrapper.tsx';
import IntroPage from '../intro/IntroPage.tsx';
import ProfilePage from '../profile/ProfilePage.tsx';
import './../../common/promiseWithResolvers.ts';

const App = () => {
    return (
        <BrowserRouter>
            <Routes>
                <Route path="/auth/login" element={<LoginPage/>}/>
                <Route path="/auth/forgot-password" element={<ForgotPasswordPage/>}/>
                <Route path="/auth/reset-password" element={<ResetPasswordPage/>}/>
                <Route path="/auth/registration/" element={<RegistrationPage/>}/>
                
                <Route path="/" element={<IntroPage />}>
                    <Route path="/" element={<Navigate to="/claims/all" replace={true} />} />
                    <Route path="/claims" element={<ClaimsWrapper />}>
                        <Route index element={<Navigate to="/claims/all" replace={true} />} />
                        <Route path="/claims/:type" element={<ClaimsPage />} />
                        <Route path="/claims/:type/:id" element={<ClaimPage />} />
                    </Route>
                    <Route path="/profile" element={<ProfilePage />}/>
                </Route>
                
                <Route path="*" element={<LoginPage/>}/>
            </Routes>
        </BrowserRouter>
    );
};

export default App;
